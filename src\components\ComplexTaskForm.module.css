/* 抽屉底部操作栏样式优化 */

/* 底部操作栏容器 */
.footerContainer {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  border-top: 1px solid #e2e8f0;
  box-shadow: 0 -4px 6px -1px rgba(0, 0, 0, 0.1), 0 -2px 4px -1px rgba(0, 0, 0, 0.06);
  padding: 20px 24px;
  position: relative;
}

.footerContainer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, #e2e8f0 50%, transparent 100%);
}

/* 按钮基础样式 */
.actionButton {
  min-width: 100px;
  height: 42px;
  border-radius: 8px;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.actionButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.actionButton:hover::before {
  left: 100%;
}

/* 取消按钮样式 */
.cancelButton {
  border: 1px solid #d1d5db;
  color: #6b7280;
  background: #ffffff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.cancelButton:hover {
  border-color: #9ca3af;
  color: #374151;
  background: #f9fafb;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.cancelButton:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

/* 重置按钮样式 */
.resetButton {
  border: 1px solid #fed7aa;
  color: #ea580c;
  background: #ffffff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.resetButton:hover {
  border-color: #fdba74;
  color: #c2410c;
  background: #fff7ed;
  box-shadow: 0 2px 4px 0 rgba(234, 88, 12, 0.1);
  transform: translateY(-1px);
}

.resetButton:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

/* 确认按钮样式（编辑模式） */
.confirmButton {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border: 1px solid #2563eb;
  color: #ffffff;
  box-shadow: 0 2px 4px 0 rgba(59, 130, 246, 0.3);
  min-width: 120px;
}

.confirmButton:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  border-color: #1d4ed8;
  box-shadow: 0 4px 8px 0 rgba(59, 130, 246, 0.4);
  transform: translateY(-1px);
}

.confirmButton:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px 0 rgba(59, 130, 246, 0.3);
}

/* 提交按钮样式（新增模式） */
.submitButton {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border: 1px solid #059669;
  color: #ffffff;
  box-shadow: 0 2px 4px 0 rgba(16, 185, 129, 0.3);
  min-width: 120px;
}

.submitButton:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  border-color: #047857;
  box-shadow: 0 4px 8px 0 rgba(16, 185, 129, 0.4);
  transform: translateY(-1px);
}

.submitButton:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px 0 rgba(16, 185, 129, 0.3);
}

/* 加载状态样式 */
.loadingButton {
  cursor: not-allowed;
  opacity: 0.8;
}

.loadingButton:hover {
  transform: none !important;
}

/* 左侧提示文本样式 */
.footerHint {
  color: #6b7280;
  font-size: 13px;
  font-weight: 400;
  display: flex;
  align-items: center;
  gap: 8px;
}

.footerHint::before {
  content: '💡';
  font-size: 16px;
}

/* 按钮组容器 */
.buttonGroup {
  display: flex;
  align-items: center;
  gap: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .footerContainer {
    padding: 16px;
  }

  .actionButton {
    min-width: 80px;
    height: 38px;
    font-size: 13px;
  }

  .buttonGroup {
    gap: 12px;
  }

  .footerHint {
    font-size: 12px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .footerContainer {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    border-top-color: #374151;
  }

  .cancelButton {
    background: #374151;
    border-color: #4b5563;
    color: #d1d5db;
  }

  .cancelButton:hover {
    background: #4b5563;
    border-color: #6b7280;
    color: #f3f4f6;
  }

  .resetButton {
    background: #374151;
    border-color: #f59e0b;
    color: #fbbf24;
  }

  .resetButton:hover {
    background: #451a03;
    border-color: #f59e0b;
  }

  .footerHint {
    color: #9ca3af;
  }
}

/* 动画效果 */
@keyframes buttonPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

.confirmButton:focus,
.submitButton:focus {
  animation: buttonPulse 1.5s infinite;
}

/* 禁用状态 */
.actionButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.actionButton:disabled:hover {
  transform: none !important;
  box-shadow: none !important;
}
